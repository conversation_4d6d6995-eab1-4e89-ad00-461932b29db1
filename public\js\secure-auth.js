// Secure Authentication Module for Naroop
// Replaces vulnerable localStorage-based authentication with secure HTTP-only cookies

import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import {
    getAuth,
    createUserWithEmailAndPassword,
    signInWithEmailAndPassword,
    signOut,
    onAuthStateChanged,
    updateProfile
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Secure Authentication Class
export class SecureAuth {
    constructor() {
        this.app = null;
        this.auth = null;
        this.initialized = false;
        this.currentUser = null;
        this.authStateListeners = [];
    }

    // Initialize Firebase with security-first approach
    async init() {
        try {
            // Fetch Firebase config securely from server
            const response = await fetch('/api/firebase-config', {
                method: 'GET',
                credentials: 'include', // Include HTTP-only cookies
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest' // CSRF protection
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch Firebase config: ${response.status}`);
            }

            const firebaseConfig = await response.json();
            
            // Validate config before using
            if (!this.validateFirebaseConfig(firebaseConfig)) {
                throw new Error('Invalid Firebase configuration received');
            }

            // Initialize Firebase
            this.app = initializeApp(firebaseConfig);
            this.auth = getAuth(this.app);

            // Set up secure auth state monitoring
            this.setupAuthStateMonitoring();
            
            this.initialized = true;
            console.log('🔐 Secure authentication initialized');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize secure authentication:', error);
            return false;
        }
    }

    // Validate Firebase configuration
    validateFirebaseConfig(config) {
        const requiredFields = ['apiKey', 'authDomain', 'projectId'];
        return requiredFields.every(field => config[field] && typeof config[field] === 'string');
    }

    // Set up secure auth state monitoring
    setupAuthStateMonitoring() {
        onAuthStateChanged(this.auth, async (user) => {
            if (user) {
                try {
                    // Get fresh ID token
                    const idToken = await user.getIdToken();
                    
                    // Send token to server for secure session creation
                    await this.createSecureSession(user, idToken);
                    
                    this.currentUser = {
                        uid: user.uid,
                        email: user.email,
                        username: user.displayName || user.email.split('@')[0],
                        emailVerified: user.emailVerified
                    };
                } catch (error) {
                    console.error('Error creating secure session:', error);
                    this.currentUser = null;
                }
            } else {
                // User signed out
                await this.clearSecureSession();
                this.currentUser = null;
            }

            // Notify listeners
            this.notifyAuthStateListeners(this.currentUser);
        });
    }

    // Create secure server-side session
    async createSecureSession(user, idToken) {
        try {
            const response = await fetch('/api/auth/create-session', {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${idToken}`,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    uid: user.uid,
                    email: user.email,
                    username: user.displayName || user.email.split('@')[0]
                })
            });

            if (!response.ok) {
                throw new Error('Failed to create secure session');
            }

            console.log('✅ Secure session created');
        } catch (error) {
            console.error('❌ Failed to create secure session:', error);
            throw error;
        }
    }

    // Clear secure server-side session
    async clearSecureSession() {
        try {
            await fetch('/api/auth/clear-session', {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            console.log('✅ Secure session cleared');
        } catch (error) {
            console.error('❌ Failed to clear secure session:', error);
        }
    }

    // Secure sign up
    async signUp(email, password, username) {
        try {
            if (!this.initialized) {
                throw new Error('Authentication not initialized');
            }

            // Validate inputs
            const validation = this.validateSignUpInputs(email, password, username);
            if (!validation.isValid) {
                return { success: false, error: validation.error };
            }

            // Create user with Firebase
            const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
            const user = userCredential.user;

            // Update profile with username
            await updateProfile(user, { displayName: username });

            return { success: true, user: this.currentUser };
        } catch (error) {
            console.error('Sign up error:', error);
            return { success: false, error: this.getErrorMessage(error.code) };
        }
    }

    // Secure sign in
    async signIn(email, password) {
        try {
            if (!this.initialized) {
                throw new Error('Authentication not initialized');
            }

            // Validate inputs
            if (!email || !password) {
                return { success: false, error: 'Email and password are required' };
            }

            // Sign in with Firebase
            const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
            
            return { success: true, user: this.currentUser };
        } catch (error) {
            console.error('Sign in error:', error);
            return { success: false, error: this.getErrorMessage(error.code) };
        }
    }

    // Secure sign out
    async signOut() {
        try {
            // Sign out from Firebase
            await signOut(this.auth);
            
            // Clear secure session is handled by auth state change
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);
            return { success: false, error: 'Failed to sign out' };
        }
    }

    // Validate sign up inputs
    validateSignUpInputs(email, password, username) {
        if (!email || !password || !username) {
            return { isValid: false, error: 'All fields are required' };
        }

        if (!this.validateEmail(email)) {
            return { isValid: false, error: 'Please enter a valid email address' };
        }

        if (password.length < 6) {
            return { isValid: false, error: 'Password must be at least 6 characters long' };
        }

        if (username.length < 2 || username.length > 30) {
            return { isValid: false, error: 'Username must be between 2 and 30 characters' };
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
            return { isValid: false, error: 'Username can only contain letters, numbers, underscores, and hyphens' };
        }

        return { isValid: true };
    }

    // Validate email format
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Get user-friendly error messages
    getErrorMessage(errorCode) {
        const errorMessages = {
            'auth/user-not-found': 'No account found with this email address',
            'auth/wrong-password': 'Incorrect password',
            'auth/email-already-in-use': 'An account with this email already exists',
            'auth/weak-password': 'Password should be at least 6 characters',
            'auth/invalid-email': 'Please enter a valid email address',
            'auth/too-many-requests': 'Too many failed attempts. Please try again later',
            'auth/network-request-failed': 'Network error. Please check your connection'
        };

        return errorMessages[errorCode] || 'An unexpected error occurred';
    }

    // Add auth state listener
    onAuthStateChanged(callback) {
        this.authStateListeners.push(callback);
        
        // Immediately call with current state
        callback(this.currentUser);
        
        // Return unsubscribe function
        return () => {
            const index = this.authStateListeners.indexOf(callback);
            if (index > -1) {
                this.authStateListeners.splice(index, 1);
            }
        };
    }

    // Notify all auth state listeners
    notifyAuthStateListeners(user) {
        this.authStateListeners.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('Error in auth state listener:', error);
            }
        });
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Make authenticated request with automatic token handling
    async makeAuthenticatedRequest(url, options = {}) {
        try {
            const response = await fetch(url, {
                ...options,
                credentials: 'include', // Include HTTP-only cookies
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                }
            });

            return response;
        } catch (error) {
            console.error('Authenticated request failed:', error);
            throw error;
        }
    }
}

// Create singleton instance
export const secureAuth = new SecureAuth();

// Make available globally for backward compatibility
window.SecureAuth = secureAuth;
