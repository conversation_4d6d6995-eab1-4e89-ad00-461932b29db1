/* Post-related styles for Naroop */

/* Create Post Section */
.create-post {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.create-post::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #8B4513, #D2691E, #c9a876);
}

.create-post:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.create-post-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.create-post-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8B4513, #D2691E);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
    flex-shrink: 0;
}

.create-post-content {
    flex: 1;
}

.create-post h3 {
    color: #1a1a1a;
    margin-bottom: 6px;
    font-size: 18px;
    font-weight: 700;
}

.create-post p {
    color: #6b7280;
    margin-bottom: 0;
    font-size: 14px;
    line-height: 1.5;
}

.create-btn {
    background: linear-gradient(135deg, #8B4513, #D2691E);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
    width: 100%;
    margin-top: 16px;
}

.create-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
    background: linear-gradient(135deg, #7a3c0f, #c55a11);
}

.create-btn:active {
    transform: translateY(0);
}

.create-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Post Cards */
.post-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
}

.post-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.post-author {
    display: flex;
    align-items: center;
    gap: 12px;
}

.author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8B4513, #D2691E);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
    flex-shrink: 0;
}

.author-info {
    flex: 1;
}

.author-name {
    font-weight: 600;
    color: #1a1a1a;
    font-size: 14px;
    margin-bottom: 2px;
}

.post-time {
    color: #6b7280;
    font-size: 12px;
}

.post-menu {
    position: relative;
}

.post-menu-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 16px;
}

.post-menu-btn:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #374151;
}

/* Post Content */
.post-content {
    margin-bottom: 16px;
}

.post-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
    line-height: 1.4;
}

.post-text {
    color: #374151;
    line-height: 1.6;
    font-size: 15px;
    margin-bottom: 12px;
}

.post-image {
    width: 100%;
    border-radius: 12px;
    margin-top: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Post Actions */
.post-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    margin-bottom: 8px;
}

.action-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
}

.action-btn:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #374151;
}

.action-btn.liked {
    color: #ef4444;
}

.action-btn.liked:hover {
    background: rgba(239, 68, 68, 0.1);
}

.action-icon {
    font-size: 16px;
}

.action-text {
    font-size: 13px;
    font-weight: 600;
}

/* Post Stats */
.post-stats {
    color: #6b7280;
    font-size: 12px;
    text-align: center;
    padding-top: 8px;
}

/* Load More Button */
.load-more {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #6b7280;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 24px auto;
    display: block;
    font-size: 14px;
}

.load-more:hover {
    background: #f9fafb;
    color: #374151;
    border-color: rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.load-more:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state h3 {
    font-size: 20px;
    margin-bottom: 8px;
    color: #374151;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.5;
}

/* Loading State */
.posts-loading {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}

.posts-loading .loading-spinner {
    margin-bottom: 16px;
}

/* Post Modal Styles */
.post-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.post-modal-content {
    background: white;
    border-radius: 20px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.post-modal-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-modal-header h3 {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
