<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>

    <!-- External CSS Files -->
    <link rel="stylesheet" href="public/css/main.css">
    <link rel="stylesheet" href="public/css/posts.css">
    <link rel="stylesheet" href="public/css/forms.css">
    <link rel="stylesheet" href="public/css/responsive.css">

    <!-- All styles moved to external CSS files for better maintainability -->
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">Naroop</div>
            <div class="nav-buttons"></div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Sidebar -->
            <aside class="sidebar">
                <h3>Navigation</h3>
                <div class="sidebar-item active" data-section="feed">Feed</div>
                <div class="sidebar-item" data-section="explore">Explore</div>
                <div class="sidebar-item" data-section="messages">Messages</div>
                <div class="sidebar-item" data-section="profile">Profile</div>
            </aside>

            <!-- Feed Section -->
            <section class="feed-section content-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>

                <div class="create-post">
                    <div class="create-post-header">
                        <div class="create-post-icon">✨</div>
                        <div class="create-post-content">
                            <h3>Share Your Story</h3>
                            <p>What positive experience would you like to share with the community today?</p>
                        </div>
                    </div>
                    <button class="create-btn" id="createPostBtn">Create Post</button>
                </div>

                <div id="postsContainer">
                    <!-- Posts will be loaded here -->
                </div>

                <button class="load-more">Load More Posts</button>
            </section>

            <!-- Explore Section -->
            <section class="content-section" id="explore-section">
                <div class="feed-header">
                    <h2 class="feed-title">Explore</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>
                <div class="explore-content">
                    <h3>🔍 Discover New Content</h3>
                    <p>Explore trending posts and discover new voices in the community.</p>
                    <div class="explore-categories">
                        <div class="category-tag">#Trending</div>
                        <div class="category-tag">#BlackExcellence</div>
                        <div class="category-tag">#CommunityLove</div>
                        <div class="category-tag">#Inspiration</div>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="content-section" id="messages-section">
                <div class="feed-header">
                    <h2 class="feed-title">Messages</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>
                <div class="messages-content">
                    <h3>💬 Your Messages</h3>
                    <p>Connect and communicate with your community.</p>
                    <div class="messages-placeholder">
                        <p>No messages yet. Start a conversation!</p>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section class="content-section" id="profile-section">
                <div class="feed-header">
                    <h2 class="feed-title">Profile</h2>
                    <button class="refresh-btn">Edit Profile</button>
                </div>
                <div class="profile-content">
                    <div class="profile-header">
                        <div class="profile-avatar">👤</div>
                        <div class="profile-info">
                            <h3 id="profileUsername">Loading...</h3>
                            <p id="profileEmail">Loading...</p>
                        </div>
                    </div>
                    <div class="profile-stats">
                        <div class="stat">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Posts</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Followers</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Following</span>
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button class="nav-btn" id="signOutBtn">Sign Out</button>
                    </div>
                </div>
            </section>

            <!-- Right Sidebar -->
            <aside class="trending">
                <h3>Trending Topics</h3>
                <div class="trending-item">
                    <div class="trending-topic">#BlackExcellence</div>
                    <div class="trending-count">2.1M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#CommunityLove</div>
                    <div class="trending-count">980K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#Inspiration</div>
                    <div class="trending-count">1.5M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#BlackJoy</div>
                    <div class="trending-count">750K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#Success</div>
                    <div class="trending-count">1.2M posts</div>
                </div>
            </aside>
        </main>

        <!-- Mobile Navigation -->
        <nav class="mobile-nav">
            <div class="mobile-nav-items">
                <div class="mobile-nav-item active" data-section="feed">
                    <div class="icon">🏠</div>
                    <div class="label">Home</div>
                </div>
                <div class="mobile-nav-item" data-section="explore">
                    <div class="icon">🔍</div>
                    <div class="label">Explore</div>
                </div>
                <div class="mobile-nav-item" data-section="messages">
                    <div class="icon">💬</div>
                    <div class="label">Messages</div>
                </div>
                <div class="mobile-nav-item" data-section="profile">
                    <div class="icon">👤</div>
                    <div class="label">Profile</div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Mobile Responsive Styles -->
    <style>
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 20px;
                margin-bottom: 20px;
            }

            .logo {
                font-size: 24px;
            }

            .nav-buttons {
                gap: 10px;
            }

            .nav-btn {
                padding: 8px 16px;
                font-size: 14px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding-bottom: 80px;
            }

            .sidebar,
            .trending {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .feed-section {
                padding: 20px;
            }

            .feed-title {
                font-size: 20px;
            }

            .refresh-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .create-post {
                padding: 20px;
            }

            .create-post-header {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .create-post h3 {
                font-size: 16px;
            }

            .create-post p {
                font-size: 13px;
            }

            .create-btn {
                padding: 12px 24px;
                font-size: 14px;
            }

            .post-card {
                padding: 20px;
                margin-bottom: 16px;
            }

            .post-title {
                font-size: 16px;
            }

            .post-text {
                font-size: 14px;
            }

            .action-btn {
                padding: 8px 12px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }

            .header {
                padding: 12px 15px;
                border-radius: 15px;
            }

            .logo {
                font-size: 20px;
            }

            .nav-btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .feed-section {
                padding: 15px;
                border-radius: 15px;
            }

            .create-post {
                padding: 16px;
                border-radius: 12px;
            }

            .create-post-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .create-post h3 {
                font-size: 15px;
            }

            .post-card {
                padding: 16px;
                border-radius: 12px;
            }

            .author-avatar {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .post-title {
                font-size: 15px;
            }

            .action-btn {
                padding: 6px 8px;
                font-size: 12px;
            }

            .mobile-nav-item .icon {
                font-size: 18px;
            }

            .mobile-nav-item .label {
                font-size: 10px;
            }
        }

        /* Create Post Modal Styles */
        .post-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .post-modal-content {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .post-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .post-modal-header h3 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 28px;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: #f5f5f5;
            color: #666;
        }

        .post-form {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px 18px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #c9a876;
            background: white;
            box-shadow: 0 0 0 3px rgba(201, 168, 118, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
            line-height: 1.5;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .cancel-btn,
        .submit-btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            min-width: 100px;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e1e5e9;
        }

        .cancel-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .submit-btn {
            background: linear-gradient(45deg, #c9a876, #d4b896);
            color: white;
            box-shadow: 0 4px 15px rgba(201, 168, 118, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(201, 168, 118, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .post-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            color: #666;
            font-weight: 500;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #c9a876;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile responsive styles for modal */
        @media (max-width: 768px) {
            .post-modal {
                padding: 10px;
            }

            .post-modal-content {
                border-radius: 15px;
                max-height: 95vh;
            }

            .post-modal-header {
                padding: 20px 20px 15px;
            }

            .post-modal-header h3 {
                font-size: 20px;
            }

            .post-form {
                padding: 20px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .form-actions {
                flex-direction: column-reverse;
                gap: 10px;
            }

            .cancel-btn,
            .submit-btn {
                width: 100%;
                padding: 15px;
            }
        }
    </style>

    <!-- JavaScript -->
    <script>
        // Authentication functions (placeholders)
        function showSignIn() {
            // Always clear any stored user/auth data and sign out from Firebase before showing sign-in UI
            console.log('🔐 Sign In button clicked - clearing auth state first...');

            if (window.FirebaseAuth && typeof window.FirebaseAuth.signOut === 'function') {
                window.FirebaseAuth.signOut().then((result) => {
                    console.log('Firebase sign out result:', result);

                    // Clear all local storage
                    if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                        window.CoreUtils.clearUserSession();
                    } else {
                        // Fallback manual clearing
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('sessionData');
                        sessionStorage.clear();
                    }

                    // Clear app state
                    if (window.AppState) {
                        window.AppState.currentUser = null;
                        window.AppState.isAuthenticated = false;
                    }

                    window.dispatchEvent(new Event('authChanged'));

                    // Redirect to landing page for proper sign-in flow
                    console.log('🔄 Redirecting to landing page for sign-in...');
                    window.location.href = '/landing.html';
                }).catch(error => {
                    console.error('Error during sign out before sign in:', error);
                    // Force clear and redirect anyway
                    if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                        window.CoreUtils.clearUserSession();
                    }
                    window.location.href = '/landing.html';
                });
            } else {
                // Firebase not available, clear manually and redirect
                if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                    window.CoreUtils.clearUserSession();
                } else {
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('sessionData');
                    sessionStorage.clear();
                    if (window.AppState) {
                        window.AppState.currentUser = null;
                        window.AppState.isAuthenticated = false;
                    }
                    window.dispatchEvent(new Event('authChanged'));
                }
                window.location.href = '/landing.html';
            }
        }

        function showSignUp() {
            alert('Sign Up functionality will be implemented with Firebase Authentication');
        }

        // Navigation functionality is now handled by navigation.js module
        // This comment replaces the duplicate navigation code that was removed

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add loading state management
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.3s ease-in-out';
        });

        // Add touch feedback for mobile
        if ('ontouchstart' in window) {
            document.addEventListener('touchstart', function() {}, {passive: true});
        }
    </script>

    <!-- Create Post Modal -->
    <div id="postModal" class="post-modal" style="display: none;">
        <div class="post-modal-content">
            <div class="post-modal-header">
                <h3>Create New Post</h3>
                <button class="close-btn" onclick="Posts.closeCreatePostModal()">&times;</button>
            </div>
            <form id="postForm" class="post-form">
                <div class="form-group">
                    <label for="postTitle">Title</label>
                    <input type="text" id="postTitle" placeholder="What's your story about?" required>
                </div>
                <div class="form-group">
                    <label for="postContent">Content</label>
                    <textarea id="postContent" placeholder="Share your positive experience..." rows="6" required></textarea>
                </div>
                <div id="postLoading" class="post-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <span>Creating your story...</span>
                </div>
                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="Posts.closeCreatePostModal()">Cancel</button>
                    <button type="submit" class="submit-btn">Share Story</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Include external JavaScript files -->
    <script type="module" src="./public/js/firebase-config.js"></script>
    <script type="module" src="./public/js/authentication.js"></script>
    <script type="module" src="./public/js/core.js"></script>
    <script type="module" src="./public/js/navigation.js"></script>
    <script type="module" src="./public/js/posts.js"></script>
    <script type="module" src="./public/js/profile.js"></script>

    <!-- Additional functionality for navigation sections -->
    <script type="module">
        import { Authentication } from './public/js/authentication.js';
        import { FirebaseAuth } from './public/js/firebase-config.js';
        import { AppState } from './public/js/core.js';

        // Initialize profile section when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load user profile data
            loadProfileData();

            // Setup sign out button
            const signOutBtn = document.getElementById('signOutBtn');
            if (signOutBtn) {
                signOutBtn.addEventListener('click', handleSignOut);
            }
        });

        // Load user profile data
        function loadProfileData() {
            try {
                const currentUser = localStorage.getItem('currentUser');
                if (currentUser) {
                    const userData = JSON.parse(currentUser);
                    
                    // Update profile UI
                    const profileUsername = document.getElementById('profileUsername');
                    const profileEmail = document.getElementById('profileEmail');
                    
                    if (profileUsername) {
                        profileUsername.textContent = userData.username || userData.displayName || 'User';
                    }
                    
                    if (profileEmail) {
                        profileEmail.textContent = userData.email || 'No email provided';
                    }
                }
            } catch (error) {
                console.error('Error loading profile data:', error);
            }
        }

        // Handle sign out
        async function handleSignOut() {
            try {
                const confirmSignOut = confirm('Are you sure you want to sign out?');
                if (!confirmSignOut) return;

                // Show loading state
                const signOutBtn = document.getElementById('signOutBtn');
                const originalText = signOutBtn.textContent;
                signOutBtn.textContent = 'Signing out...';
                signOutBtn.disabled = true;

                // Sign out using Firebase Auth
                const result = await FirebaseAuth.signOut();
                
                if (result.success) {
                    // Clear local storage
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');
                    
                    // Redirect to landing page
                    window.location.href = '/landing.html';
                } else {
                    alert('Error signing out. Please try again.');
                    signOutBtn.textContent = originalText;
                    signOutBtn.disabled = false;
                }
            } catch (error) {
                console.error('Sign out error:', error);
                alert('Error signing out. Please try again.');
                
                // Reset button
                const signOutBtn = document.getElementById('signOutBtn');
                signOutBtn.textContent = 'Sign Out';
                signOutBtn.disabled = false;
            }
        }

        // Update profile data when navigation changes to profile section
        window.addEventListener('hashchange', function() {
            if (window.location.hash === '#profile') {
                loadProfileData();
            }
        });
    </script>

    <!-- Duplicate script loading removed - modules are loaded above -->
</body>
</html>
