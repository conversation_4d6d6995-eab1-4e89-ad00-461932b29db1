/* Responsive Design for Naroop */

/* Tablet Styles */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 250px 1fr 250px;
        gap: 20px;
        padding: 0 15px;
    }
    
    .sidebar {
        padding: 20px;
    }
    
    .header {
        padding: 15px 25px;
        margin-bottom: 25px;
    }
    
    .logo {
        font-size: 28px;
    }
}

/* Small Tablet / Large Mobile */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0;
    }
    
    .header {
        padding: 15px 20px;
        margin-bottom: 20px;
        border-radius: 15px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .logo {
        font-size: 24px;
    }
    
    .nav-buttons {
        width: 100%;
        justify-content: center;
    }
    
    .nav-btn {
        flex: 1;
        max-width: 120px;
        padding: 8px 16px;
        font-size: 13px;
    }
    
    /* Hide desktop sidebar on mobile */
    .sidebar {
        display: none;
    }
    
    /* Show mobile navigation */
    .mobile-nav {
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding: 10px 0;
        z-index: 1000;
        justify-content: space-around;
        align-items: center;
    }
    
    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #6b7280;
        font-size: 12px;
        font-weight: 500;
        text-decoration: none;
        min-width: 60px;
    }
    
    .mobile-nav-item:hover,
    .mobile-nav-item.active {
        background: rgba(139, 69, 19, 0.1);
        color: #8B4513;
    }
    
    .mobile-nav-item::before {
        font-size: 20px;
        margin-bottom: 4px;
    }
    
    .mobile-nav-item:nth-child(1)::before { content: "🏠"; }
    .mobile-nav-item:nth-child(2)::before { content: "🔍"; }
    .mobile-nav-item:nth-child(3)::before { content: "💬"; }
    .mobile-nav-item:nth-child(4)::before { content: "👤"; }
    
    /* Add bottom padding to main content for mobile nav */
    .main-content {
        padding-bottom: 80px;
    }
    
    /* Feed adjustments */
    .feed-section {
        max-width: 100%;
    }
    
    .feed-header {
        margin-bottom: 20px;
        padding-bottom: 12px;
    }
    
    .feed-title {
        font-size: 24px;
    }
    
    .refresh-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    /* Post cards mobile optimization */
    .post-card {
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
    
    .post-header {
        margin-bottom: 12px;
    }
    
    .author-avatar {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
    
    .author-name {
        font-size: 13px;
    }
    
    .post-time {
        font-size: 11px;
    }
    
    .post-title {
        font-size: 16px;
        margin-bottom: 6px;
    }
    
    .post-text {
        font-size: 14px;
    }
    
    .post-actions {
        padding-top: 10px;
        margin-bottom: 6px;
    }
    
    .action-btn {
        padding: 6px 10px;
        font-size: 13px;
    }
    
    .action-icon {
        font-size: 14px;
    }
    
    .action-text {
        font-size: 12px;
    }
    
    /* Create post mobile */
    .create-post {
        padding: 16px;
        margin-bottom: 20px;
        border-radius: 12px;
    }
    
    .create-post-header {
        gap: 12px;
        margin-bottom: 16px;
    }
    
    .create-post-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .create-post h3 {
        font-size: 16px;
    }
    
    .create-post p {
        font-size: 13px;
    }
    
    .create-btn {
        padding: 10px 20px;
        font-size: 13px;
        margin-top: 12px;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .header {
        padding: 12px 15px;
        margin-bottom: 15px;
        border-radius: 12px;
    }
    
    .logo {
        font-size: 22px;
    }
    
    .nav-btn {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 20px;
    }
    
    .main-content {
        gap: 15px;
        padding-bottom: 70px;
    }
    
    .feed-title {
        font-size: 20px;
    }
    
    .refresh-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .post-card {
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 10px;
    }
    
    .post-title {
        font-size: 15px;
    }
    
    .post-text {
        font-size: 13px;
    }
    
    .create-post {
        padding: 12px;
        margin-bottom: 15px;
    }
    
    .create-post-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
    
    .create-post h3 {
        font-size: 15px;
    }
    
    .create-post p {
        font-size: 12px;
    }
    
    .mobile-nav {
        padding: 8px 0;
    }
    
    .mobile-nav-item {
        padding: 6px 8px;
        font-size: 11px;
        min-width: 50px;
    }
    
    .mobile-nav-item::before {
        font-size: 18px;
        margin-bottom: 2px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .main-content {
        grid-template-columns: 280px 1fr 280px;
        gap: 30px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .sidebar {
        padding: 30px;
    }
    
    .feed-section {
        max-width: 650px;
    }
}

/* Ultra-wide screens */
@media (min-width: 1600px) {
    .main-content {
        max-width: 1400px;
        grid-template-columns: 320px 1fr 320px;
        gap: 40px;
    }
    
    .feed-section {
        max-width: 700px;
    }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .header {
        padding: 10px 20px;
        margin-bottom: 15px;
    }
    
    .main-content {
        padding-bottom: 60px;
    }
    
    .mobile-nav {
        padding: 6px 0;
    }
    
    .mobile-nav-item {
        padding: 4px 8px;
        font-size: 10px;
    }
    
    .mobile-nav-item::before {
        font-size: 16px;
        margin-bottom: 2px;
    }
}

/* Print styles */
@media print {
    .header,
    .sidebar,
    .mobile-nav,
    .create-post,
    .post-actions,
    .refresh-btn,
    .load-more {
        display: none !important;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        gap: 0;
        padding: 0;
    }
    
    .post-card {
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    
    .post-card:hover {
        transform: none;
    }
    
    body {
        background: white;
    }
}

/* High DPI / Retina displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo,
    .post-title,
    .feed-title {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles would go here */
    /* Currently not implemented but structure is ready */
}
