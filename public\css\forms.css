/* Form and Modal styles for Naroop */

/* Form Styles */
.post-form {
    padding: 20px 24px 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: inherit;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #8B4513;
    background: white;
    box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.form-group input.error,
.form-group textarea.error {
    border-color: #ef4444;
    background: #fef2f2;
}

.form-group input.error:focus,
.form-group textarea.error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group input.success,
.form-group textarea.success {
    border-color: #10b981;
    background: #f0fdf4;
}

.form-group input.success:focus,
.form-group textarea.success:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-group textarea {
    min-height: 120px;
    line-height: 1.6;
}

/* Field Validation Messages */
.field-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 6px;
    display: none;
    font-weight: 500;
}

.field-error.show {
    display: block;
}

.field-success {
    color: #10b981;
    font-size: 12px;
    margin-top: 6px;
    display: none;
    font-weight: 500;
}

.field-success.show {
    display: block;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.cancel-btn {
    background: transparent;
    border: 2px solid #d1d5db;
    color: #6b7280;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.cancel-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.submit-btn {
    background: linear-gradient(135deg, #8B4513, #D2691E);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
    background: linear-gradient(135deg, #7a3c0f, #c55a11);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.2);
}

/* Loading States */
.post-loading {
    display: none;
    text-align: center;
    padding: 20px;
    color: #6b7280;
}

.post-loading.show {
    display: block;
}

.post-loading .loading-spinner {
    margin-bottom: 12px;
}

.post-loading span {
    font-size: 14px;
    font-weight: 500;
}

/* Error and Success Messages */
.form-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
}

.form-message.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.form-message.success {
    background: #f0fdf4;
    color: #059669;
    border: 1px solid #bbf7d0;
}

.form-message.warning {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.form-message.info {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

/* Character Counter */
.char-counter {
    text-align: right;
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.char-counter.warning {
    color: #d97706;
}

.char-counter.error {
    color: #dc2626;
}

/* File Upload Styles */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f9fafb;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.file-upload:hover .file-upload-btn {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

.file-upload.dragover .file-upload-btn {
    background: rgba(139, 69, 19, 0.1);
    border-color: #8B4513;
    color: #8B4513;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #f3f4f6;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 12px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #8B4513, #D2691E);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Responsive Form Styles */
@media (max-width: 768px) {
    .form-actions {
        flex-direction: column-reverse;
    }
    
    .cancel-btn,
    .submit-btn {
        width: 100%;
        justify-content: center;
    }
    
    .post-modal-content {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .post-modal-header {
        padding: 20px 20px 16px;
    }
    
    .post-form {
        padding: 16px 20px 20px;
    }
}

/* Focus Management */
.form-group input:focus,
.form-group textarea:focus,
.cancel-btn:focus,
.submit-btn:focus {
    outline: 2px solid #8B4513;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .form-group input,
    .form-group textarea {
        border-width: 3px;
    }
    
    .form-group input:focus,
    .form-group textarea:focus {
        box-shadow: 0 0 0 4px rgba(139, 69, 19, 0.2);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .form-group input,
    .form-group textarea,
    .cancel-btn,
    .submit-btn,
    .file-upload-btn {
        transition: none;
    }
    
    .submit-btn:hover {
        transform: none;
    }
}
